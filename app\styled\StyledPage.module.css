/* CSS Module for StyledPage */

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.button {
  background-color: #28a745;
  color: white;
  border: 2px solid #1e7e34;
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  margin: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button:hover {
  background-color: #218838;
  border-color: #1e7e34;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.buttonSecondary {
  background-color: #6c757d;
  border-color: #5a6268;
}

.buttonSecondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.content {
  margin: 2rem 0;
  line-height: 1.6;
}

.title {
  color: #343a40;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

/* Responsive Design - Mobile First Approach */
@media (max-width: 768px) {
  .container {
    margin: 1rem;
    padding: 1rem;
    border-radius: 8px;
  }
  
  .button {
    width: 100%;
    margin: 5px 0;
    padding: 14px 20px;
    font-size: 1rem;
  }
  
  .title {
    font-size: 1.5rem;
    text-align: center;
  }
  
  .content {
    margin: 1rem 0;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 0.5rem;
    padding: 0.75rem;
  }
  
  .title {
    font-size: 1.3rem;
  }
  
  .content {
    font-size: 0.9rem;
  }
}

/* Tablet styles */
@media (min-width: 769px) and (max-width: 1024px) {
  .container {
    margin: 1.5rem auto;
    padding: 1.5rem;
  }
  
  .button {
    margin: 8px;
    padding: 10px 20px;
  }
}
