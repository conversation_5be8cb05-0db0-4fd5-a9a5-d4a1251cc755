'use client';

import React, { useState } from 'react';
import styled from 'styled-components';
import styles from './StyledPage.module.css';

// Styled Components with dynamic styling
const StyledButton = styled.button`
  background-color: ${props => props.bgColor || '#007bff'};
  color: ${props => props.textColor || 'white'};
  border: 2px solid ${props => props.borderColor || '#0056b3'};
  padding: 12px 24px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  margin: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: ${props => props.hoverColor || '#0056b3'};
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const StyledContainer = styled.div`
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
  border-radius: 12px;
  margin: 2rem 0;
  color: white;
  text-align: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
`;

export default function StyledPage() {
  // State for dynamic styling
  const [buttonColor, setButtonColor] = useState('#007bff');
  const [buttonText, setButtonText] = useState('Click me!');

  // Color options for dynamic styling
  const colorOptions = [
    { bg: '#007bff', hover: '#0056b3', text: 'Blue Button' },
    { bg: '#28a745', hover: '#1e7e34', text: 'Green Button' },
    { bg: '#dc3545', hover: '#c82333', text: 'Red Button' },
    { bg: '#ffc107', hover: '#e0a800', text: 'Yellow Button', textColor: '#000' },
    { bg: '#6f42c1', hover: '#5a32a3', text: 'Purple Button' }
  ];

  const handleColorChange = () => {
    const randomColor = colorOptions[Math.floor(Math.random() * colorOptions.length)];
    setButtonColor(randomColor.bg);
    setButtonText(randomColor.text);
  };

  // Inline styles for the header
  const headerStyle = {
    backgroundColor: '#4a90e2',
    fontSize: '2.5rem',
    padding: '2rem',
    color: 'white',
    textAlign: 'center',
    margin: '0',
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
  };

  return (
    <div>
      <header style={headerStyle}>
        Styled Page - Lab 9
      </header>
      <main>
        <div className={styles.container}>
          <h2 className={styles.title}>CSS Modules Demo</h2>
          <div className={styles.content}>
            <p>This page demonstrates various styling techniques in Next.js including:</p>
            <ul>
              <li>✅ Inline styles (header above)</li>
              <li>✅ CSS Modules (this container and buttons)</li>
              <li>🔄 Styled Components (coming next)</li>
              <li>✅ Responsive design (resize your window!)</li>
            </ul>
          </div>

          <div>
            <button className={styles.button}>
              Primary CSS Module Button
            </button>
            <button className={`${styles.button} ${styles.buttonSecondary}`}>
              Secondary CSS Module Button
            </button>
          </div>
        </div>

        <StyledContainer>
          <h2>Styled Components Demo</h2>
          <p>These buttons use styled-components with dynamic prop-based styling!</p>

          <div>
            <StyledButton
              bgColor={buttonColor}
              hoverColor={colorOptions.find(c => c.bg === buttonColor)?.hover}
              textColor={colorOptions.find(c => c.bg === buttonColor)?.textColor}
              onClick={handleColorChange}
            >
              {buttonText}
            </StyledButton>

            <StyledButton
              bgColor="#17a2b8"
              hoverColor="#138496"
              borderColor="#117a8b"
            >
              Info Styled Button
            </StyledButton>

            <StyledButton
              bgColor="#6c757d"
              hoverColor="#5a6268"
              borderColor="#545b62"
            >
              Secondary Styled Button
            </StyledButton>
          </div>

          <p style={{ marginTop: '1rem', fontSize: '0.9rem', opacity: 0.9 }}>
            Click the first button to see dynamic color changes!
          </p>
        </StyledContainer>
      </main>
    </div>
  );
}
